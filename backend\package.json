{"name": "crypto-signal-bot-backend", "version": "1.0.0", "description": "Backend API for crypto trading bot", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "jobs:start": "node dist/jobs/index.js", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.4", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "axios": "^1.6.2", "sqlite3": "^5.1.6", "node-cron": "^3.0.3", "express-rate-limit": "^7.1.5", "compression": "^1.7.4", "morgan": "^1.10.0", "uuid": "^9.0.1", "lodash": "^4.17.21", "moment": "^2.29.4"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/morgan": "^1.9.9", "@types/uuid": "^9.0.7", "@types/lodash": "^4.14.202", "@types/node": "^20.10.4", "@types/node-cron": "^3.0.11", "typescript": "^5.3.3", "nodemon": "^3.0.2", "ts-node": "^10.9.1", "jest": "^29.7.0", "@types/jest": "^29.5.8", "ts-jest": "^29.1.1", "eslint": "^8.55.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1"}, "engines": {"node": ">=18.0.0"}}