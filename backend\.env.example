# Server Configuration
PORT=3001
NODE_ENV=development

# CoinGecko API
COINGECKO_API_KEY=your_api_key_here
COINGECKO_BASE_URL=https://api.coingecko.com/api/v3

# Database
DATABASE_URL=./data/trading.db

# Trading Configuration
UPDATE_INTERVAL=60000
PAPER_TRADING=true
AUTO_EXECUTE=false
MIN_CONFIDENCE=70

# Risk Management
MAX_POSITIONS=5
MAX_POSITION_SIZE=0.2
STOP_LOSS_PERCENTAGE=5
TAKE_PROFIT_PERCENTAGE=15
MAX_DAILY_LOSS=10

# Rate Limiting
RATE_LIMIT_WINDOW_MS=3600000
RATE_LIMIT_MAX_REQUESTS=100

# CORS
CORS_ORIGIN=http://localhost:3000

# Logging
LOG_LEVEL=info
