# Crypto Signal Bot

A comprehensive algorithmic crypto trading bot with real-time dashboard, technical analysis, and automated trading signals.

## 🚀 Features

- **Real-time Dashboard**: Live crypto prices, trading signals, and portfolio tracking
- **Technical Analysis**: Golden Cross, RSI, MACD, and volume-based strategies
- **Paper Trading**: Risk-free virtual trading with $10,000 starting capital
- **Backtesting Engine**: Test strategies against historical data
- **Alert System**: Real-time notifications for trading opportunities
- **WebSocket Integration**: Live updates without page refresh
- **Risk Management**: Stop-loss, take-profit, and position sizing controls

## 🛠️ Tech Stack

### Frontend
- **Next.js 15** with TypeScript
- **Tailwind CSS** + shadcn/ui components
- **Recharts** for data visualization
- **Socket.io Client** for real-time updates
- **Zustand** for state management

### Backend
- **Node.js** with Express.js
- **TypeScript** for type safety
- **Socket.io** for WebSocket communication
- **SQLite** for local data storage
- **Node-cron** for scheduled tasks
- **CoinGecko API** for crypto data

## 📁 Project Structure

```
crypto-signal-bot/
├── frontend/                 # Next.js frontend application
│   ├── src/
│   │   ├── components/      # React components
│   │   ├── lib/            # Utility functions
│   │   └── app/            # Next.js app router
├── backend/                 # Node.js backend API
│   ├── src/
│   │   ├── controllers/    # API route handlers
│   │   ├── services/       # Business logic
│   │   ├── algorithms/     # Trading algorithms
│   │   ├── data/          # Data storage
│   │   └── jobs/          # Scheduled tasks
└── shared/                 # Shared TypeScript types
```

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and npm 9+
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd crypto-signal-bot
   ```

2. **Install dependencies**
   ```bash
   npm run install:all
   ```

3. **Set up environment variables**
   ```bash
   # Backend
   cd backend
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Start development servers**
   ```bash
   # From root directory
   npm run dev
   ```

   This will start:
   - Frontend: http://localhost:3000
   - Backend: http://localhost:3001

### Environment Variables

Create `backend/.env` with:

```env
# Server Configuration
PORT=3001
NODE_ENV=development

# CoinGecko API (optional for basic usage)
COINGECKO_API_KEY=your_api_key_here

# Trading Configuration
PAPER_TRADING=true
AUTO_EXECUTE=false
MIN_CONFIDENCE=70

# Risk Management
MAX_POSITIONS=5
STOP_LOSS_PERCENTAGE=5
TAKE_PROFIT_PERCENTAGE=15
```

## 📊 Trading Strategies

### 1. Golden Cross Strategy
- **Signal**: MA50 crosses above/below MA200
- **Confidence**: Based on volume and momentum
- **Risk**: Medium

### 2. RSI Strategy
- **Buy**: RSI < 30 (oversold)
- **Sell**: RSI > 70 (overbought)
- **Risk**: Low

### 3. MACD Strategy
- **Signal**: MACD line crosses signal line
- **Confirmation**: Histogram direction
- **Risk**: Medium

### 4. Volume Spike Strategy
- **Trigger**: Volume > 150% of average
- **Confirmation**: Price momentum
- **Risk**: High

## 🔧 Development

### Available Scripts

```bash
# Development
npm run dev              # Start both frontend and backend
npm run dev:frontend     # Start only frontend
npm run dev:backend      # Start only backend

# Building
npm run build           # Build both applications
npm run build:frontend  # Build frontend only
npm run build:backend   # Build backend only

# Testing
npm run test           # Run all tests
npm run test:frontend  # Run frontend tests
npm run test:backend   # Run backend tests
```

### API Endpoints

```
GET  /api/cryptos              # Top cryptocurrencies
GET  /api/signals              # Current trading signals
GET  /api/signals/:crypto      # Signals for specific crypto
POST /api/backtest             # Run strategy backtest
GET  /api/portfolio            # Portfolio status
POST /api/alerts/subscribe     # Subscribe to alerts
GET  /api/performance          # Performance metrics
GET  /health                   # Health check
```

### WebSocket Events

```javascript
// Client to Server
socket.emit('subscribe', { channel: 'prices' })
socket.emit('unsubscribe', { channel: 'signals' })

// Server to Client
socket.on('price_update', (data) => { /* handle price update */ })
socket.on('signal_update', (data) => { /* handle new signal */ })
socket.on('alert', (data) => { /* handle alert */ })
```

## 📈 Performance Metrics

The bot tracks comprehensive performance metrics:

- **Win Rate**: Percentage of profitable trades
- **Total Return**: Overall portfolio performance
- **Sharpe Ratio**: Risk-adjusted returns
- **Max Drawdown**: Largest peak-to-trough decline
- **Average Win/Loss**: Mean profit/loss per trade

## 🔒 Risk Management

- **Stop Loss**: Automatic -5% exit
- **Take Profit**: Automatic +15% exit
- **Position Sizing**: Max 20% per crypto
- **Max Positions**: 5 concurrent trades
- **Daily Loss Limit**: -10% portfolio value

## 🚀 Deployment

### Vercel (Recommended)

1. **Frontend**: Deploy to Vercel with automatic GitHub integration
2. **Backend**: Use Vercel Serverless Functions
3. **Database**: Vercel KV for caching
4. **Cron Jobs**: Vercel Cron for price updates

### Environment Variables for Production

```env
NODE_ENV=production
COINGECKO_API_KEY=your_production_key
DATABASE_URL=your_production_db_url
CORS_ORIGIN=https://your-domain.com
```

## 📝 License

This project is licensed under the MIT License.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Open a Pull Request

## 📞 Support

For questions and support, please open an issue on GitHub.

---

**⚠️ Disclaimer**: This bot is for educational purposes. Cryptocurrency trading involves substantial risk. Never trade with money you can't afford to lose.
