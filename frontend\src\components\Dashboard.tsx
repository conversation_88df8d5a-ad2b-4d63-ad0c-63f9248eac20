'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { formatCurrency, formatPercentage, getSignalColor, getSignalIcon } from '@/lib/utils'

// Mock data for initial development
const mockCryptos = [
  {
    id: 'bitcoin',
    symbol: 'BTC',
    name: 'Bitcoin',
    current_price: 43250.50,
    price_change_percentage_24h: 2.45,
    market_cap: 847000000000,
    volume_24h: 15600000000
  },
  {
    id: 'ethereum',
    symbol: 'ETH',
    name: 'Ethereum',
    current_price: 2650.75,
    price_change_percentage_24h: -1.23,
    market_cap: 318000000000,
    volume_24h: 8900000000
  },
  {
    id: 'binancecoin',
    symbol: 'BNB',
    name: 'BNB',
    current_price: 315.20,
    price_change_percentage_24h: 0.87,
    market_cap: 47000000000,
    volume_24h: 1200000000
  }
]

const mockSignals = [
  {
    id: '1',
    crypto: 'Bitcoin',
    symbol: 'BTC',
    type: 'BUY' as const,
    confidence: 85,
    price: 43250.50,
    timestamp: new Date(),
    strategy: 'Golden Cross',
    message: 'MA50 crossed above MA200 with strong volume'
  },
  {
    id: '2',
    crypto: 'Ethereum',
    symbol: 'ETH',
    type: 'HOLD' as const,
    confidence: 65,
    price: 2650.75,
    timestamp: new Date(),
    strategy: 'RSI Strategy',
    message: 'RSI in neutral zone, waiting for clear signal'
  }
]

const mockPortfolio = {
  totalValue: 12450.75,
  cash: 2500.00,
  totalReturn: 24.5,
  dailyReturn: 1.8
}

export default function Dashboard() {
  const [cryptos, setCryptos] = useState(mockCryptos)
  const [signals, setSignals] = useState(mockSignals)
  const [portfolio, setPortfolio] = useState(mockPortfolio)
  const [isConnected, setIsConnected] = useState(false)

  useEffect(() => {
    // TODO: Connect to WebSocket and fetch real data
    setIsConnected(true)
  }, [])

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Crypto Trading Bot Dashboard</h1>
          <p className="text-gray-600 mt-2">
            Real-time algorithmic trading signals and portfolio management
          </p>
          <div className="flex items-center mt-4">
            <div className={`w-3 h-3 rounded-full mr-2 ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
            <span className="text-sm text-gray-600">
              {isConnected ? 'Connected' : 'Disconnected'}
            </span>
          </div>
        </div>

        {/* Portfolio Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Total Portfolio</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(portfolio.totalValue)}</div>
              <p className="text-xs text-gray-600 mt-1">
                Cash: {formatCurrency(portfolio.cash)}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Total Return</CardTitle>
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${portfolio.totalReturn >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {formatPercentage(portfolio.totalReturn)}
              </div>
              <p className="text-xs text-gray-600 mt-1">All time</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Daily P&L</CardTitle>
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${portfolio.dailyReturn >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {formatPercentage(portfolio.dailyReturn)}
              </div>
              <p className="text-xs text-gray-600 mt-1">Today</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Active Signals</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{signals.length}</div>
              <p className="text-xs text-gray-600 mt-1">Current opportunities</p>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Crypto Prices */}
          <Card>
            <CardHeader>
              <CardTitle>Top Cryptocurrencies</CardTitle>
              <CardDescription>Real-time prices and 24h changes</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {cryptos.map((crypto) => (
                  <div key={crypto.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                        <span className="text-xs font-bold">{crypto.symbol}</span>
                      </div>
                      <div>
                        <div className="font-medium">{crypto.name}</div>
                        <div className="text-sm text-gray-600">{crypto.symbol}</div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium">{formatCurrency(crypto.current_price)}</div>
                      <div className={`text-sm ${crypto.price_change_percentage_24h >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {crypto.price_change_percentage_24h >= 0 ? '+' : ''}
                        {formatPercentage(crypto.price_change_percentage_24h)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Trading Signals */}
          <Card>
            <CardHeader>
              <CardTitle>Trading Signals</CardTitle>
              <CardDescription>AI-generated buy/sell recommendations</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {signals.map((signal) => (
                  <div key={signal.id} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <span className="text-lg">{getSignalIcon(signal.type)}</span>
                        <span className="font-medium">{signal.crypto}</span>
                        <span className={`text-sm font-bold ${getSignalColor(signal.type)}`}>
                          {signal.type}
                        </span>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium">{formatCurrency(signal.price)}</div>
                        <div className="text-xs text-gray-600">{signal.confidence}% confidence</div>
                      </div>
                    </div>
                    <div className="text-sm text-gray-600 mb-2">{signal.message}</div>
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-gray-500">{signal.strategy}</span>
                      <Button size="sm" variant="outline">
                        View Details
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
