import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatCurrency(value: number, currency: string = 'USD'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 6,
  }).format(value)
}

export function formatPercentage(value: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'percent',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(value / 100)
}

export function formatNumber(value: number): string {
  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  }).format(value)
}

export function formatLargeNumber(value: number): string {
  if (value >= 1e9) {
    return `${(value / 1e9).toFixed(2)}B`
  } else if (value >= 1e6) {
    return `${(value / 1e6).toFixed(2)}M`
  } else if (value >= 1e3) {
    return `${(value / 1e3).toFixed(2)}K`
  }
  return value.toString()
}

export function getSignalColor(type: 'BUY' | 'SELL' | 'HOLD'): string {
  switch (type) {
    case 'BUY':
      return 'text-green-500'
    case 'SELL':
      return 'text-red-500'
    case 'HOLD':
      return 'text-yellow-500'
    default:
      return 'text-gray-500'
  }
}

export function getSignalIcon(type: 'BUY' | 'SELL' | 'HOLD'): string {
  switch (type) {
    case 'BUY':
      return '🟢'
    case 'SELL':
      return '🔴'
    case 'HOLD':
      return '🟡'
    default:
      return '⚪'
  }
}

export function calculatePnL(entryPrice: number, currentPrice: number, quantity: number): {
  pnl: number;
  pnlPercentage: number;
} {
  const pnl = (currentPrice - entryPrice) * quantity
  const pnlPercentage = ((currentPrice - entryPrice) / entryPrice) * 100
  
  return { pnl, pnlPercentage }
}

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout)
    }
    
    timeout = setTimeout(() => {
      func(...args)
    }, wait)
  }
}

export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => (inThrottle = false), limit)
    }
  }
}
